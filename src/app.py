# Copyright (c) 2025 Neco Glitched
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program. If not, see <https://www.gnu.org/licenses/>.

# https://github.com/zauberzeug/nicegui/issues/1841#issuecomment-**********
import multiprocessing

multiprocessing.set_start_method("spawn", force=True)

from nicegui import app, ui

# Add static file serving for assets
app.add_static_files("/assets", "src/assets")

# Add custom fonts CSS
ui.add_head_html("""
<style>
@font-face {
    font-family: 'TF2Build';
    src: url('/assets/tf2build.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'TF2Secondary';
    src: url('/assets/TF2secondary.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
</style>
""")

with ui.row().classes("w-full"):
    with (
        ui.column().classes("w-1/3"),
        ui.card(align_items="center").classes("w-full h-[80vh] no-shadow"),
    ):
        ui.label("Play").classes("text-xl text-left w-full").style(
            "font-family: 'TF2Build'",
        )
        ui.button("Play Game", color="#37322c").classes(
            "w-full font-family: 'TF2Secondary'",
        )
        ui.button("Options", color="#37322c").classes(
            "w-full font-family: 'TF2Secondary'",
        )
        ui.button("3", color="#37322c").classes("w-full font-family: 'TF2Secondary'")
    ui.space()
    with ui.column().classes("w-1/3"):
        ui.label("Right Sidebar").classes("text-xl")

ui.run(
    title="TF Launch",
    native=True,
    dark=True,
)
