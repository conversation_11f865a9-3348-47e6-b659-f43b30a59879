{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "material-icon-theme.folders.associations": {
            "tflaunch": "app",
        },
        "[python]": {
            "diffEditor.ignoreTrimWhitespace": false,
            "editor.defaultColorDecorators": "never",
            "gitlens.codeLens.symbolScopes": [
                "!Module"
            ],
            "editor.formatOnType": true,
            "editor.wordBasedSuggestions": "off",
            "editor.defaultFormatter": "charliermarsh.ruff",
            "editor.formatOnSave": true,
            "editor.codeActionsOnSave": {
                "source.fixAll": "explicit",
                "source.organizeImports": "never"
            }
        },
    }
}